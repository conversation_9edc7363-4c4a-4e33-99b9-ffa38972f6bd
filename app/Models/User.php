<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Validator;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'phone_number',
        'password',
        'user_type',
        'role_id',
        'is_active',
        'last_login_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
        'last_login_at' => 'datetime'
    ];


    public static function uploadImage($file)
    {      

        if (empty($file)) 

            return [NULL,NULL];

        $destinationPath = public_path('files');

        $ext = $file->getClientOriginalExtension();

        $file_url=time().Str::random(12).'.'.$ext;

        $file->move($destinationPath,$file_url);
        
        return $file_url;

    }

    public static function getValidationMessage($request,$rules) {

        $val = Validator::make($request->all(),$rules);

        $message = NULL;

        if ($val->fails()) {           

            $errors = $val->errors()->toArray();

            if (isset($errors['name']))

                $message .= $errors['name'][0]." ";
                
            if (isset($errors['password']))

                $message .= $errors['password'][0]." ";

            if (isset($errors['phone_number']))

                $message .= $errors['phone_number'][0]." ";

            if (isset($errors['old_password']))

                $message .= $errors['old_password'][0]." ";

            

        }
        
        return $message;
        
    }

    function lastUpdated() {
        $votes = Vote::latest('updated_at')->first();

        if ($votes) {
            return $votes->updated_at->format('M d, Y h:i A');
        } else {
            return "No vote updated yet.";
        }
    }
}
