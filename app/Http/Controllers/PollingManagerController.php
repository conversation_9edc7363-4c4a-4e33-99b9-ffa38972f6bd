<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\Candidate;
use App\Models\Eveidence;
use App\Models\PollingStation;
use App\Models\Position;
use App\Models\SpoiledVote;
use App\Models\User;
use App\Models\Vote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PollingManagerController extends Controller
{
    /**
     * Display the polling manager dashboard
     */
    public function dashboard(Request $request)
    {
        // Get filter parameters
        $search = $request->get('search');
        $district = $request->get('district');
        $county = $request->get('county');
        $subcounty = $request->get('subcounty');
        $status = $request->get('status'); // all, submitted, pending

        // Build polling stations query
        $query = PollingStation::with(['agent.user', 'agent.votes', 'agent.eveidences']);

        // Apply filters
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('district', 'like', "%{$search}%")
                  ->orWhere('county', 'like', "%{$search}%")
                  ->orWhere('subcounty', 'like', "%{$search}%");
            });
        }

        if ($district) {
            $query->where('district', $district);
        }

        if ($county) {
            $query->where('county', $county);
        }

        if ($subcounty) {
            $query->where('subcounty', $subcounty);
        }

        // Apply status filter
        if ($status === 'submitted') {
            $query->whereHas('agent.votes');
        } elseif ($status === 'pending') {
            $query->whereDoesntHave('agent.votes');
        }

        $pollingStations = $query->paginate(20)->withQueryString();

        // Get filter options
        $districts = PollingStation::distinct()->pluck('district')->filter()->sort();
        $counties = PollingStation::distinct()->pluck('county')->filter()->sort();
        $subcounties = PollingStation::distinct()->pluck('subcounty')->filter()->sort();

        // Get positions for vote submission
        $positions = Position::with('candidates')->get();

        // Get summary statistics
        $totalStations = PollingStation::count();
        $submittedStations = PollingStation::whereHas('agent.votes')->count();
        $pendingStations = $totalStations - $submittedStations;
        $totalVotes = Vote::sum('number_of_votes');

        return view('manager.dashboard', compact(
            'pollingStations',
            'districts',
            'counties',
            'subcounties',
            'positions',
            'totalStations',
            'submittedStations',
            'pendingStations',
            'totalVotes'
        ));
    }

    /**
     * Show vote submission form for a specific polling station
     */
    public function showVoteForm(PollingStation $station)
    {
        $positions = Position::with('candidates')->get();
        $agent = $station->agent;
        
        // Get existing votes for this station
        $existingVotes = [];
        if ($agent) {
            $existingVotes = Vote::where('agent_id', $agent->id)
                ->pluck('number_of_votes', 'candidate_id')
                ->toArray();
        }

        return view('manager.vote-form', compact('station', 'positions', 'agent', 'existingVotes'));
    }

    /**
     * Submit votes for a polling station
     */
    public function submitVotes(Request $request, PollingStation $station)
    {
        $request->validate([
            'votes' => 'required|array',
            'votes.*' => 'required|integer|min:0',
        ]);

        $agent = $station->agent;
        if (!$agent) {
            return redirect()->back()->with('error', 'No agent assigned to this polling station.');
        }

        DB::beginTransaction();
        try {
            foreach ($request->votes as $candidateId => $voteCount) {
                // Update or create vote record
                Vote::updateOrCreate(
                    [
                        'agent_id' => $agent->id,
                        'candidate_id' => $candidateId
                    ],
                    [
                        'number_of_votes' => $voteCount,
                        'latitude' => $station->latitude,
                        'longitude' => $station->longitude,
                    ]
                );
            }

            DB::commit();
            return redirect()->route('manager.dashboard')
                ->with('success', "Votes submitted successfully for {$station->name}");

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Failed to submit votes. Please try again.')
                ->withInput();
        }
    }

    /**
     * Show evidence upload form for a polling station
     */
    public function showEvidenceForm(PollingStation $station)
    {
        $agent = $station->agent;
        $existingEvidence = [];
        
        if ($agent) {
            $existingEvidence = $agent->eveidences;
        }

        return view('manager.evidence-form', compact('station', 'agent', 'existingEvidence'));
    }

    /**
     * Upload evidence for a polling station
     */
    public function uploadEvidence(Request $request, PollingStation $station)
    {
        $request->validate([
            'evidence_files.*' => 'required|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
            'file_names.*' => 'nullable|string|max:255',
        ]);

        $agent = $station->agent;
        if (!$agent) {
            return redirect()->back()->with('error', 'No agent assigned to this polling station.');
        }

        $uploadedFiles = [];
        
        if ($request->hasFile('evidence_files')) {
            foreach ($request->file('evidence_files') as $index => $file) {
                $fileName = $request->file_names[$index] ?? 'Evidence ' . ($index + 1);
                
                $evidence = new Eveidence();
                $evidence->file_url = User::uploadImage($file);
                $evidence->file_name = $fileName;
                $evidence->agent_id = $agent->id;
                $evidence->save();
                
                $uploadedFiles[] = $fileName;
            }
        }

        $message = 'Evidence uploaded successfully for ' . $station->name;
        if (!empty($uploadedFiles)) {
            $message .= ': ' . implode(', ', $uploadedFiles);
        }

        return redirect()->route('manager.dashboard')->with('success', $message);
    }

    /**
     * Submit spoiled votes for a polling station
     */
    public function submitSpoiledVotes(Request $request, PollingStation $station)
    {
        $request->validate([
            'spoiled_votes' => 'required|array',
            'spoiled_votes.*.position_id' => 'required|exists:positions,id',
            'spoiled_votes.*.number_of_votes' => 'required|integer|min:0',
            'spoiled_votes.*.remarks' => 'nullable|string|max:500',
        ]);

        $agent = $station->agent;
        if (!$agent) {
            return redirect()->back()->with('error', 'No agent assigned to this polling station.');
        }

        DB::beginTransaction();
        try {
            foreach ($request->spoiled_votes as $spoiledVote) {
                if ($spoiledVote['number_of_votes'] > 0) {
                    SpoiledVote::updateOrCreate(
                        [
                            'polling_station_id' => $station->id,
                            'position_id' => $spoiledVote['position_id'],
                            'agent_id' => $agent->id,
                        ],
                        [
                            'number_of_votes' => $spoiledVote['number_of_votes'],
                            'remarks' => $spoiledVote['remarks'] ?? null,
                        ]
                    );
                }
            }

            DB::commit();
            return redirect()->route('manager.dashboard')
                ->with('success', "Spoiled votes submitted successfully for {$station->name}");

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Failed to submit spoiled votes. Please try again.')
                ->withInput();
        }
    }

    /**
     * Get polling station details via AJAX
     */
    public function getStationDetails(PollingStation $station)
    {
        $station->load(['agent.user', 'agent.votes.candidate', 'agent.eveidences']);
        
        return response()->json([
            'station' => $station,
            'vote_summary' => $this->getVoteSummary($station),
            'evidence_count' => $station->agent ? $station->agent->eveidences->count() : 0,
        ]);
    }

    /**
     * Get vote summary for a station
     */
    private function getVoteSummary(PollingStation $station)
    {
        if (!$station->agent) {
            return [];
        }

        $votes = Vote::where('agent_id', $station->agent->id)
            ->with('candidate.position')
            ->get()
            ->groupBy('candidate.position.name');

        $summary = [];
        foreach ($votes as $position => $positionVotes) {
            $summary[$position] = [
                'total_votes' => $positionVotes->sum('number_of_votes'),
                'candidates' => $positionVotes->map(function($vote) {
                    return [
                        'name' => $vote->candidate->name,
                        'votes' => $vote->number_of_votes
                    ];
                })
            ];
        }

        return $summary;
    }
}
