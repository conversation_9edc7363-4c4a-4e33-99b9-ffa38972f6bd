<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\Vote;
use App\Services\VoteAuditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class VoteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /** 
     * Record Vote
     * @group Votes 
     * @bodyParam candidate_id integer required
     * @bodyParam number_of_votes integer required
     * @bodyParam latitude double
     * @bodyParam longitude double
     * @response   {
     *       "status": "success",
     *       "message": "Vote Saved successfully"
     *   }
    **/
    public function store(Request $request)
    {
        $rules = [
            'candidate_id'=>'required',
            'number_of_votes'=>'required|numeric|min:0',
            'latitude'=>'nullable|numeric',
            'longitude'=>'nullable|numeric',
        ];

        $this->validate($request,$rules);

        $agent = Agent::where('user_id',Auth::id())->first();

        if(!$agent){
            return response()->json(['status' => 'failed', 'message' => 'Only Agents can post Election results'],422);
        }

        DB::beginTransaction();
        try {
            // Get existing vote record
            $existingVote = Vote::where('agent_id', $agent->id)
                ->where('candidate_id', $request->candidate_id)
                ->first();

            $actionType = $existingVote ? 'update' : 'create';

            // Log the submission in audit trail BEFORE updating the vote
            $auditService = new VoteAuditService();
            $auditService->logVoteSubmission(
                $agent->id,
                $request->candidate_id,
                $request->number_of_votes,
                $actionType,
                'api', // Since this is API endpoint
                $request,
                "Vote submitted via mobile app"
            );

            // Create or update vote record AFTER logging
            $vote = Vote::updateOrCreate(
                [
                    'agent_id' => $agent->id,
                    'candidate_id' => $request->candidate_id
                ],
                [
                    'number_of_votes' => $request->number_of_votes,
                    'latitude' => $request->latitude,
                    'longitude' => $request->longitude,
                ]
            );

            DB::commit();

            if($request->ajax()){
                return response()->json([
                    'status' => 'success',
                    'message' => 'Vote saved and logged successfully',
                    'data' => $vote,
                    'action' => $actionType
                ], 200);
            }

            return back()->with('success', 'Vote saved successfully');

        } catch (\Exception $e) {
            DB::rollback();

            if($request->ajax()){
                return response()->json([
                    'status' => 'failed',
                    'message' => 'Failed to save vote: ' . $e->getMessage()
                ], 500);
            }

            return back()->with('error', 'Failed to save vote');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Vote $vote)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Vote $vote)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Vote $vote)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vote $vote)
    {
        //
    }
}
