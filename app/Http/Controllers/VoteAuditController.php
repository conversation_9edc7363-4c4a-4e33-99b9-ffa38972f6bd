<?php

namespace App\Http\Controllers;

use App\Models\VoteAuditLog;
use App\Models\Agent;
use App\Models\PollingStation;
use App\Services\VoteAuditService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class VoteAuditController extends Controller
{
    protected $auditService;

    public function __construct(VoteAuditService $auditService)
    {
        $this->auditService = $auditService;
    }

    /**
     * Display the main audit dashboard
     */
    public function index(Request $request)
    {
        $search = $request->get('search');
        $status = $request->get('status'); // all, flagged, verified, unverified
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $agentId = $request->get('agent_id');
        $stationId = $request->get('station_id');

        // Build query
        $query = VoteAuditLog::with([
            'agent.user',
            'candidate.position',
            'pollingStation',
            'submittedBy',
            'verifiedBy',
            'flaggedBy'
        ]);

        // Apply filters
        if ($search) {
            $query->whereHas('agent.user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%");
            })->orWhereHas('pollingStation', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('candidate', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        if ($status === 'flagged') {
            $query->flagged();
        } elseif ($status === 'verified') {
            $query->verified();
        } elseif ($status === 'unverified') {
            $query->unverified();
        }

        if ($dateFrom) {
            $query->where('submission_time', '>=', Carbon::parse($dateFrom));
        }

        if ($dateTo) {
            $query->where('submission_time', '<=', Carbon::parse($dateTo)->endOfDay());
        }

        if ($agentId) {
            $query->where('agent_id', $agentId);
        }

        if ($stationId) {
            $query->where('polling_station_id', $stationId);
        }

        $auditLogs = $query->orderBy('submission_time', 'desc')->paginate(25)->withQueryString();

        // Get summary statistics
        $suspiciousActivity = $this->auditService->getSuspiciousActivitySummary();
        
        // Get filter options
        $agents = Agent::with('user')->get();
        $stations = PollingStation::all();

        return view('admin.audit.index', compact(
            'auditLogs',
            'suspiciousActivity',
            'agents',
            'stations'
        ));
    }

    /**
     * Show detailed audit trail for a specific agent
     */
    public function agentAudit(Agent $agent)
    {
        $auditTrail = $this->auditService->getAgentAuditTrail($agent->id);
        
        return view('admin.audit.agent', compact('agent', 'auditTrail'));
    }

    /**
     * Show detailed audit trail for a specific polling station
     */
    public function stationAudit(PollingStation $station)
    {
        $auditTrail = $this->auditService->getPollingStationAuditTrail($station->id);
        
        return view('admin.audit.station', compact('station', 'auditTrail'));
    }

    /**
     * Show flagged submissions that need review
     */
    public function flagged()
    {
        $flaggedSubmissions = $this->auditService->getFlaggedSubmissions();
        $suspiciousActivity = $this->auditService->getSuspiciousActivitySummary();
        
        return view('admin.audit.flagged', compact('flaggedSubmissions', 'suspiciousActivity'));
    }

    /**
     * Show unverified submissions
     */
    public function unverified()
    {
        $unverifiedSubmissions = $this->auditService->getUnverifiedSubmissions();
        
        return view('admin.audit.unverified', compact('unverifiedSubmissions'));
    }

    /**
     * Verify a submission
     */
    public function verify(Request $request, VoteAuditLog $auditLog)
    {
        $request->validate([
            'verification_notes' => 'nullable|string|max:1000'
        ]);

        $success = $this->auditService->verifySubmission(
            $auditLog->id,
            $request->verification_notes
        );

        if ($success) {
            return redirect()->back()->with('success', 'Submission verified successfully.');
        }

        return redirect()->back()->with('error', 'Failed to verify submission.');
    }

    /**
     * Flag a submission
     */
    public function flag(Request $request, VoteAuditLog $auditLog)
    {
        $request->validate([
            'flag_reason' => 'required|in:multiple_rapid_submissions,large_vote_change,unusual_timing,manual_flag,other',
            'flag_notes' => 'nullable|string|max:1000'
        ]);

        $success = $this->auditService->flagSubmission(
            $auditLog->id,
            $request->flag_reason,
            $request->flag_notes
        );

        if ($success) {
            return redirect()->back()->with('success', 'Submission flagged successfully.');
        }

        return redirect()->back()->with('error', 'Failed to flag submission.');
    }

    /**
     * Unflag a submission
     */
    public function unflag(Request $request, VoteAuditLog $auditLog)
    {
        $request->validate([
            'unflag_notes' => 'nullable|string|max:1000'
        ]);

        $success = $this->auditService->unflagSubmission(
            $auditLog->id,
            $request->unflag_notes
        );

        if ($success) {
            return redirect()->back()->with('success', 'Submission unflagged successfully.');
        }

        return redirect()->back()->with('error', 'Failed to unflag submission.');
    }

    /**
     * Get audit statistics for reporting
     */
    public function statistics(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30));
        $endDate = $request->get('end_date', Carbon::now());

        if (is_string($startDate)) {
            $startDate = Carbon::parse($startDate);
        }
        if (is_string($endDate)) {
            $endDate = Carbon::parse($endDate);
        }

        $statistics = $this->auditService->getAuditStatistics($startDate, $endDate);
        
        return view('admin.audit.statistics', compact('statistics', 'startDate', 'endDate'));
    }

    /**
     * Export audit data
     */
    public function export(Request $request)
    {
        $request->validate([
            'format' => 'required|in:csv,excel',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date',
            'status' => 'nullable|in:all,flagged,verified,unverified'
        ]);

        // This would implement export functionality
        // For now, return a simple response
        return response()->json([
            'message' => 'Export functionality will be implemented',
            'parameters' => $request->all()
        ]);
    }

    /**
     * Get audit data via API for charts/widgets
     */
    public function apiData(Request $request)
    {
        $type = $request->get('type', 'summary');
        
        switch ($type) {
            case 'summary':
                return response()->json($this->auditService->getSuspiciousActivitySummary());
                
            case 'recent':
                $recent = VoteAuditLog::recent()
                    ->with(['agent.user', 'candidate.position', 'pollingStation'])
                    ->orderBy('submission_time', 'desc')
                    ->limit(10)
                    ->get();
                return response()->json($recent);
                
            case 'flagged':
                $flagged = $this->auditService->getFlaggedSubmissions(10);
                return response()->json($flagged);
                
            default:
                return response()->json(['error' => 'Invalid type parameter'], 400);
        }
    }
}
