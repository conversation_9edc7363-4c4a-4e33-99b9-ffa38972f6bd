<?php

namespace App\Services;

use App\Models\Vote;
use App\Models\VoteAuditLog;
use App\Models\Agent;
use App\Models\Candidate;
use App\Models\PollingStation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class VoteAuditService
{
    /**
     * Log a vote submission with full audit trail
     */
    public function logVoteSubmission(
        int $agentId,
        int $candidateId,
        int $newVotes,
        string $actionType = 'create',
        string $submissionMethod = 'web',
        ?Request $request = null,
        ?string $notes = null
    ): VoteAuditLog {
        // Get previous vote count
        $previousVote = Vote::where('agent_id', $agentId)
            ->where('candidate_id', $candidateId)
            ->first();
        
        $previousVotes = $previousVote ? $previousVote->number_of_votes : 0;
        $voteDifference = $newVotes - $previousVotes;

        // Get related entities
        $agent = Agent::with(['polling_station', 'user'])->find($agentId);
        $candidate = Candidate::with('position')->find($candidateId);
        
        // Determine who submitted the votes
        $submittedByUserId = Auth::id() ?? $agent->user_id; // Fallback to agent's user
        $submittedByUserType = Auth::user() ? Auth::user()->user_type : $agent->user->user_type;

        // Get request details if available
        $ipAddress = $request ? $request->ip() : null;
        $userAgent = $request ? $request->userAgent() : null;
        $latitude = $request ? $request->input('latitude') : null;
        $longitude = $request ? $request->input('longitude') : null;

        // Create audit log entry
        $auditLog = VoteAuditLog::create([
            'agent_id' => $agentId,
            'candidate_id' => $candidateId,
            'polling_station_id' => $agent->polling_station_id,
            'position_id' => $candidate->position_id,
            'previous_votes' => $previousVotes,
            'new_votes' => $newVotes,
            'vote_difference' => $voteDifference,
            'action_type' => $actionType,
            'submission_method' => $submissionMethod,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'submitted_by_user_id' => $submittedByUserId,
            'submitted_by_user_type' => $submittedByUserType,
            'submission_time' => now(),
            'notes' => $notes
        ]);

        // Auto-flag if suspicious
        $auditLog->autoFlag();

        return $auditLog;
    }

    /**
     * Get audit trail for a specific agent
     */
    public function getAgentAuditTrail(int $agentId, ?int $limit = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = VoteAuditLog::with([
            'candidate.position',
            'submittedBy',
            'verifiedBy',
            'flaggedBy'
        ])
        ->where('agent_id', $agentId)
        ->orderBy('submission_time', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get audit trail for a specific polling station
     */
    public function getPollingStationAuditTrail(int $pollingStationId, ?int $limit = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = VoteAuditLog::with([
            'agent.user',
            'candidate.position',
            'submittedBy',
            'verifiedBy',
            'flaggedBy'
        ])
        ->where('polling_station_id', $pollingStationId)
        ->orderBy('submission_time', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get audit trail for a specific candidate
     */
    public function getCandidateAuditTrail(int $candidateId, ?int $limit = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = VoteAuditLog::with([
            'agent.user',
            'pollingStation',
            'submittedBy',
            'verifiedBy',
            'flaggedBy'
        ])
        ->where('candidate_id', $candidateId)
        ->orderBy('submission_time', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get flagged submissions that need review
     */
    public function getFlaggedSubmissions(?int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return VoteAuditLog::with([
            'agent.user',
            'candidate.position',
            'pollingStation',
            'submittedBy',
            'flaggedBy'
        ])
        ->flagged()
        ->orderBy('flagged_at', 'desc')
        ->limit($limit)
        ->get();
    }

    /**
     * Get submissions that need verification
     */
    public function getUnverifiedSubmissions(?int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return VoteAuditLog::with([
            'agent.user',
            'candidate.position',
            'pollingStation',
            'submittedBy'
        ])
        ->unverified()
        ->orderBy('submission_time', 'desc')
        ->limit($limit)
        ->get();
    }

    /**
     * Get suspicious activity summary
     */
    public function getSuspiciousActivitySummary(): array
    {
        $flaggedCount = VoteAuditLog::flagged()->count();
        $rapidSubmissions = VoteAuditLog::flagged()
            ->where('flag_reason', 'multiple_rapid_submissions')
            ->count();
        $largeChanges = VoteAuditLog::flagged()
            ->where('flag_reason', 'large_vote_change')
            ->count();
        $unusualTiming = VoteAuditLog::flagged()
            ->where('flag_reason', 'unusual_timing')
            ->count();

        $multipleSubmissions = VoteAuditLog::multipleSubmissions()->get();

        return [
            'total_flagged' => $flaggedCount,
            'rapid_submissions' => $rapidSubmissions,
            'large_changes' => $largeChanges,
            'unusual_timing' => $unusualTiming,
            'agents_with_multiple_submissions' => $multipleSubmissions->count(),
            'recent_flagged' => VoteAuditLog::flagged()->recent()->count()
        ];
    }

    /**
     * Verify a submission
     */
    public function verifySubmission(int $auditLogId, ?string $notes = null): bool
    {
        $auditLog = VoteAuditLog::find($auditLogId);
        
        if (!$auditLog) {
            return false;
        }

        $auditLog->update([
            'is_verified' => true,
            'verified_by_user_id' => Auth::id(),
            'verified_at' => now(),
            'verification_notes' => $notes
        ]);

        return true;
    }

    /**
     * Flag a submission manually
     */
    public function flagSubmission(int $auditLogId, string $reason, ?string $notes = null): bool
    {
        $auditLog = VoteAuditLog::find($auditLogId);
        
        if (!$auditLog) {
            return false;
        }

        $auditLog->update([
            'is_flagged' => true,
            'flag_reason' => $reason,
            'flag_notes' => $notes,
            'flagged_by_user_id' => Auth::id(),
            'flagged_at' => now()
        ]);

        return true;
    }

    /**
     * Unflag a submission
     */
    public function unflagSubmission(int $auditLogId, ?string $notes = null): bool
    {
        $auditLog = VoteAuditLog::find($auditLogId);
        
        if (!$auditLog) {
            return false;
        }

        $auditLog->update([
            'is_flagged' => false,
            'flag_reason' => null,
            'flag_notes' => $notes,
            'flagged_by_user_id' => null,
            'flagged_at' => null
        ]);

        return true;
    }

    /**
     * Get audit statistics for a time period
     */
    public function getAuditStatistics(Carbon $startDate, Carbon $endDate): array
    {
        $submissions = VoteAuditLog::withinTimeRange($startDate, $endDate);

        return [
            'total_submissions' => $submissions->count(),
            'unique_agents' => $submissions->distinct('agent_id')->count(),
            'unique_stations' => $submissions->distinct('polling_station_id')->count(),
            'flagged_submissions' => $submissions->flagged()->count(),
            'verified_submissions' => $submissions->verified()->count(),
            'api_submissions' => $submissions->where('submission_method', 'api')->count(),
            'web_submissions' => $submissions->where('submission_method', 'web')->count(),
            'manager_submissions' => $submissions->where('submission_method', 'manager_portal')->count(),
            'average_votes_per_submission' => $submissions->avg('new_votes'),
            'total_vote_changes' => $submissions->sum('vote_difference')
        ];
    }
}
