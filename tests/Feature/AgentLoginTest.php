<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AgentLoginTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that agent users are redirected to agent_dashboard after login
     */
    public function test_agent_redirected_to_agent_dashboard_after_login()
    {
        // Create a polling station first
        $pollingStation = \App\Models\PollingStation::create([
            'name' => 'Test Polling Station',
            'constituency' => 'Test Constituency',
            'district' => 'Test District',
            'county' => 'Test County',
            'subcounty' => 'Test Subcounty',
            'parish' => 'Test Parish',
            'village' => 'Test Village',
        ]);

        // Create an agent user
        $user = User::create([
            'name' => 'Test Agent',
            'phone_number' => '0700000000',
            'password' => Hash::make('password'),
            'user_type' => 'agent',
            'is_active' => true,
        ]);

        // Create the agent record linked to the user and polling station
        \App\Models\Agent::create([
            'user_id' => $user->id,
            'polling_station_id' => $pollingStation->id,
        ]);

        // Attempt to login
        $response = $this->post('/login', [
            'phone_number' => '0700000000',
            'password' => 'password',
        ]);

        // Should redirect to agent_dashboard
        $response->assertRedirect('/agent_dashboard');
        $this->assertAuthenticatedAs($user);
    }

    /**
     * Test that non-agent users are redirected to home after login
     */
    public function test_non_agent_redirected_to_home_after_login()
    {
        // Create a manager user
        $manager = User::create([
            'name' => 'Test Manager',
            'phone_number' => '0700000001',
            'password' => Hash::make('password'),
            'user_type' => 'manager',
            'is_active' => true,
        ]);

        // Attempt to login
        $response = $this->post('/login', [
            'phone_number' => '0700000001',
            'password' => 'password',
        ]);

        // Should redirect to home
        $response->assertRedirect('/home');
        $this->assertAuthenticatedAs($manager);
    }

    /**
     * Test that agent users can access agent_dashboard
     */
    public function test_agent_can_access_agent_dashboard()
    {
        // Create a polling station first
        $pollingStation = \App\Models\PollingStation::create([
            'name' => 'Test Polling Station',
            'constituency' => 'Test Constituency',
            'district' => 'Test District',
            'county' => 'Test County',
            'subcounty' => 'Test Subcounty',
            'parish' => 'Test Parish',
            'village' => 'Test Village',
        ]);

        // Create an agent user
        $user = User::create([
            'name' => 'Test Agent',
            'phone_number' => '0700000000',
            'password' => Hash::make('password'),
            'user_type' => 'agent',
            'is_active' => true,
        ]);

        // Create the agent record linked to the user and polling station
        $agent = \App\Models\Agent::create([
            'user_id' => $user->id,
            'polling_station_id' => $pollingStation->id,
        ]);

        // Login as agent
        $this->actingAs($user);

        // Access agent dashboard
        $response = $this->get('/agent_dashboard');

        // Should be successful
        $response->assertStatus(200);
    }

    /**
     * Test that agent users cannot access main dashboard and are redirected with error message
     */
    public function test_agent_cannot_access_main_dashboard()
    {
        // Create a polling station first
        $pollingStation = \App\Models\PollingStation::create([
            'name' => 'Test Polling Station',
            'constituency' => 'Test Constituency',
            'district' => 'Test District',
            'county' => 'Test County',
            'subcounty' => 'Test Subcounty',
            'parish' => 'Test Parish',
            'village' => 'Test Village',
        ]);

        // Create an agent user
        $user = User::create([
            'name' => 'Test Agent',
            'phone_number' => '0700000000',
            'password' => Hash::make('password'),
            'user_type' => 'agent',
            'is_active' => true,
        ]);

        // Create the agent record linked to the user and polling station
        \App\Models\Agent::create([
            'user_id' => $user->id,
            'polling_station_id' => $pollingStation->id,
        ]);

        // Login as agent
        $this->actingAs($user);

        // Try to access main dashboard
        $response = $this->get('/home');

        // Should redirect to agent dashboard with error message
        $response->assertRedirect('/agent_dashboard');
        $response->assertSessionHas('error', 'You do not have permission to access that page.');
    }
}
