<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class FlashMessageTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that permission denied redirects with flash message instead of 403
     */
    public function test_permission_denied_shows_flash_message_instead_of_403()
    {
        // Create a polling station first
        $pollingStation = \App\Models\PollingStation::create([
            'name' => 'Test Polling Station',
            'constituency' => 'Test Constituency',
            'district' => 'Test District',
            'county' => 'Test County',
            'subcounty' => 'Test Subcounty',
            'parish' => 'Test Parish',
            'village' => 'Test Village',
        ]);

        // Create an agent user (who doesn't have view_dashboard permission)
        $user = User::create([
            'name' => 'Test Agent',
            'phone_number' => '0700000000',
            'password' => Hash::make('password'),
            'user_type' => 'agent',
            'is_active' => true,
        ]);

        // Create the agent record
        \App\Models\Agent::create([
            'user_id' => $user->id,
            'polling_station_id' => $pollingStation->id,
        ]);

        // Login as agent
        $this->actingAs($user);

        // Try to access a route that requires view_dashboard permission
        $response = $this->get('/home');

        // Should redirect to agent dashboard with error flash message
        $response->assertRedirect('/agent_dashboard');
        $response->assertSessionHas('error', 'You do not have permission to access that page.');
    }

    /**
     * Test that different user types get redirected to appropriate pages
     */
    public function test_different_user_types_redirect_to_appropriate_pages()
    {
        // Test with manager user trying to access admin-only route
        $manager = User::create([
            'name' => 'Test Manager',
            'phone_number' => '0700000001',
            'password' => Hash::make('password'),
            'user_type' => 'manager',
            'is_active' => true,
        ]);

        $this->actingAs($manager);

        // Try to access admin users route (requires view_users permission which managers have)
        // Let's try a route that requires a permission managers don't have
        $response = $this->get('/admin/roles');

        // Should redirect to home with error message (managers don't have role management permissions)
        $response->assertRedirect('/home');
        $response->assertSessionHas('error', 'You do not have permission to access that page.');
    }

    /**
     * Test flash message types work correctly
     */
    public function test_flash_message_types()
    {
        $user = User::create([
            'name' => 'Test User',
            'phone_number' => '0700000002',
            'password' => Hash::make('password'),
            'user_type' => 'admin',
            'is_active' => true,
        ]);

        $this->actingAs($user);

        // Test different flash message types
        $types = ['error', 'success', 'warning', 'info'];

        foreach ($types as $type) {
            $response = $this->get("/test-flash/{$type}");
            $response->assertRedirect();
            $response->assertSessionHas($type);
        }
    }
}
