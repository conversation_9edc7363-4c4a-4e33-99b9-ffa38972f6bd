<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the user_type enum to include all user types
        DB::statement("ALTER TABLE users MODIFY COLUMN user_type ENUM('admin', 'agent', 'manager', 'viewer') NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First, update any manager/viewer users to agent type before reverting enum
        DB::statement("UPDATE users SET user_type = 'agent' WHERE user_type IN ('manager', 'viewer')");

        // Then revert back to original enum values
        DB::statement("ALTER TABLE users MODIFY COLUMN user_type ENUM('admin', 'agent') NOT NULL");
    }
};
