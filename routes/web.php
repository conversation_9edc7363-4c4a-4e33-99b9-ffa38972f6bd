<?php

use App\Http\Controllers\AgentController;
use App\Http\Controllers\CandidateController;
use App\Http\Controllers\EveidenceController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PollingStationController;
use App\Http\Controllers\PositionController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\SpoiledVoteController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserManagementController;
use App\Http\Controllers\VoteController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
 

Route::get('/', function () {

    if(Auth::guest())

        return view('auth.login');

    // Redirect users based on their type
    $userType = Auth::user()->user_type;
    switch ($userType) {
        case 'agent':
            return redirect('agent_dashboard');
        case 'manager':
            return redirect('manager/dashboard');
        default:
            return redirect('home');
    }

});

Auth::routes();

Route::group(['middleware' => ['auth']], function () {

    // Dashboard routes (accessible to all authenticated users with view_dashboard permission)
    Route::middleware(['permission:view_dashboard'])->group(function () {
        Route::get('/home', [HomeController::class, 'index'])->name('home');
        Route::get('/vote-trends-data/{positionId?}', [HomeController::class, 'getVoteTrendsData'])->name('vote.trends.data');
        Route::get('/api/polling-stations-map-data', [PollingStationController::class, 'getMapData'])->name('polling.stations.map.data');
        Route::get('/api/dashboard-stats', [HomeController::class, 'getDashboardStats'])->name('api.dashboard.stats');
        Route::get('/api/monitoring-data', [\App\Http\Controllers\CandidateMonitoringController::class, 'getMonitoringData'])->name('api.monitoring.data');
        Route::get('/api/station-evidence/{stationId}', [PollingStationController::class, 'getStationEvidence'])->name('api.station.evidence');
    });

    // Position management routes
    Route::middleware(['permission:view_positions'])->group(function () {
        Route::resource('positions', PositionController::class)->except(['create', 'store', 'edit', 'update', 'destroy']);
        Route::get('/ajax_get_candidates/{postion_id}', [PositionController::class, 'ajaxGetCandidates'])->name('ajax_get_candidates');
    });
    Route::middleware(['permission:create_positions'])->group(function () {
        Route::get('positions/create', [PositionController::class, 'create'])->name('positions.create');
        Route::post('positions', [PositionController::class, 'store'])->name('positions.store');
    });
    Route::middleware(['permission:edit_positions'])->group(function () {
        Route::get('positions/{position}/edit', [PositionController::class, 'edit'])->name('positions.edit');
        Route::put('positions/{position}', [PositionController::class, 'update'])->name('positions.update');
    });
    Route::middleware(['permission:delete_positions'])->group(function () {
        Route::delete('positions/{position}', [PositionController::class, 'destroy'])->name('positions.destroy');
    });

    // Candidate management routes
    Route::middleware(['permission:view_candidates'])->group(function () {
        Route::resource('candidates', CandidateController::class)->except(['create', 'store', 'edit', 'update', 'destroy']);
    });
    Route::middleware(['permission:create_candidates'])->group(function () {
        Route::get('candidates/create', [CandidateController::class, 'create'])->name('candidates.create');
        Route::post('candidates', [CandidateController::class, 'store'])->name('candidates.store');
    });
    Route::middleware(['permission:edit_candidates'])->group(function () {
        Route::get('candidates/{candidate}/edit', [CandidateController::class, 'edit'])->name('candidates.edit');
        Route::put('candidates/{candidate}', [CandidateController::class, 'update'])->name('candidates.update');
    });
    Route::middleware(['permission:delete_candidates'])->group(function () {
        Route::delete('candidates/{candidate}', [CandidateController::class, 'destroy'])->name('candidates.destroy');
    });

    // Polling station management routes
    Route::middleware(['permission:view_polling_stations'])->group(function () {
        Route::resource('polling_stations', PollingStationController::class)->except(['create', 'store', 'edit', 'update', 'destroy']);
    });
    Route::middleware(['permission:create_polling_stations'])->group(function () {
        Route::get('polling_stations/create', [PollingStationController::class, 'create'])->name('polling_stations.create');
        Route::post('polling_stations', [PollingStationController::class, 'store'])->name('polling_stations.store');
    });
    Route::middleware(['permission:edit_polling_stations'])->group(function () {
        Route::get('polling_stations/{polling_station}/edit', [PollingStationController::class, 'edit'])->name('polling_stations.edit');
        Route::put('polling_stations/{polling_station}', [PollingStationController::class, 'update'])->name('polling_stations.update');
    });
    Route::middleware(['permission:delete_polling_stations'])->group(function () {
        Route::delete('polling_stations/{polling_station}', [PollingStationController::class, 'destroy'])->name('polling_stations.destroy');
    });

    // Agent management routes (for polling station managers and admins)
    Route::middleware(['permission:view_users'])->group(function () {
        Route::resource('agents', AgentController::class)->except(['create', 'store', 'edit', 'update', 'destroy']);
    });
    Route::middleware(['permission:create_users'])->group(function () {
        Route::get('agents/create', [AgentController::class, 'create'])->name('agents.create');
        Route::post('agents', [AgentController::class, 'store'])->name('agents.store');
    });
    Route::middleware(['permission:edit_users'])->group(function () {
        Route::get('agents/{agent}/edit', [AgentController::class, 'edit'])->name('agents.edit');
        Route::put('agents/{agent}', [AgentController::class, 'update'])->name('agents.update');
    });
    Route::middleware(['permission:delete_users'])->group(function () {
        Route::delete('agents/{agent}', [AgentController::class, 'destroy'])->name('agents.destroy');
    });

    // Vote management routes
    Route::middleware(['permission:view_votes'])->group(function () {
        Route::resource('votes', VoteController::class)->except(['create', 'store', 'edit', 'update', 'destroy']);
    });
    Route::middleware(['permission:submit_votes'])->group(function () {
        Route::get('votes/create', [VoteController::class, 'create'])->name('votes.create');
        Route::post('votes', [VoteController::class, 'store'])->name('votes.store');
    });
    Route::middleware(['permission:edit_votes'])->group(function () {
        Route::get('votes/{vote}/edit', [VoteController::class, 'edit'])->name('votes.edit');
        Route::put('votes/{vote}', [VoteController::class, 'update'])->name('votes.update');
    });
    Route::middleware(['permission:delete_votes'])->group(function () {
        Route::delete('votes/{vote}', [VoteController::class, 'destroy'])->name('votes.destroy');
    });

    // Evidence management routes
    Route::middleware(['permission:upload_evidence'])->group(function () {
        Route::resource('evedence', EveidenceController::class);
    });

    // Spoiled votes management
    Route::middleware(['permission:manage_votes'])->group(function () {
        Route::resource('spoiled_votes', SpoiledVoteController::class);
    });

    // Legacy user routes (keep for backward compatibility)
    Route::middleware(['permission:view_users'])->group(function () {
        Route::resource('users', UserController::class);
    });
    
    // Agent-specific routes
    Route::middleware(['role:agent'])->group(function () {
        Route::get('/agent_dashboard', [HomeController::class, 'agentDashboard'])->name('agent_dashboard');
    });


    Route::get('/ajax_get_candidates/{postion_id}', [PositionController::class, 'ajaxGetCandidates'])->name('ajax_get_candidates');

    // Test route for flash messages (can be removed in production)
    Route::get('/test-flash/{type}', function($type) {
        $messages = [
            'error' => 'This is a test error message!',
            'success' => 'This is a test success message!',
            'warning' => 'This is a test warning message!',
            'info' => 'This is a test info message!'
        ];

        $message = $messages[$type] ?? 'Unknown message type';
        return redirect()->back()->with($type, $message);
    })->name('test.flash');

    // Polling Manager Routes
    Route::middleware(['role:polling_station_manager'])->prefix('manager')->name('manager.')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\PollingManagerController::class, 'dashboard'])->name('dashboard');
        Route::get('/station/{station}/vote-form', [App\Http\Controllers\PollingManagerController::class, 'showVoteForm'])->name('vote-form');
        Route::post('/station/{station}/submit-votes', [App\Http\Controllers\PollingManagerController::class, 'submitVotes'])->name('submit-votes');
        Route::get('/station/{station}/evidence-form', [App\Http\Controllers\PollingManagerController::class, 'showEvidenceForm'])->name('evidence-form');
        Route::post('/station/{station}/upload-evidence', [App\Http\Controllers\PollingManagerController::class, 'uploadEvidence'])->name('upload-evidence');
        Route::post('/station/{station}/submit-spoiled-votes', [App\Http\Controllers\PollingManagerController::class, 'submitSpoiledVotes'])->name('submit-spoiled-votes');
        Route::get('/station/{station}/details', [App\Http\Controllers\PollingManagerController::class, 'getStationDetails'])->name('station-details');
    });
    
    // Candidate Monitoring Routes
    Route::get('/monitoring', [\App\Http\Controllers\CandidateMonitoringController::class, 'index'])->name('monitoring.index');
    Route::post('/monitoring/add/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'addPreferred'])->name('monitoring.add');
    Route::delete('/monitoring/remove/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'removePreferred'])->name('monitoring.remove');
    Route::post('/monitoring/toggle/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'togglePreferred'])->name('monitoring.toggle');
    Route::post('/monitoring/settings/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'updateSettings'])->name('monitoring.settings');
    Route::get('/monitoring/compare/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'compare'])->name('monitoring.compare');
    Route::get('/monitoring/guide', function() { return view('monitoring.guide'); })->name('monitoring.guide');
    
    // Notification routes
    Route::get('/notifications', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::get('/notifications/count', [App\Http\Controllers\NotificationController::class, 'getCount'])->name('notifications.count');
    Route::post('/notifications/{id}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/read-all', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.read-all');

    // User Management Routes (Admin only)
    Route::prefix('admin')->name('admin.')->middleware(['permission:view_users'])->group(function () {
        Route::resource('users', UserManagementController::class);
        Route::patch('users/{user}/toggle-status', [UserManagementController::class, 'toggleStatus'])->name('users.toggle-status');
        Route::post('users/{user}/assign-roles', [UserManagementController::class, 'assignRoles'])->name('users.assign-roles');

        // Role Management Routes
        Route::resource('roles', RoleController::class);

        // Permissions Info Route
        Route::get('permissions-info', function () {
            return view('admin.permissions-info');
        })->name('permissions.info');
    });

});
