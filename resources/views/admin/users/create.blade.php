@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-person-plus-fill text-primary me-2"></i>
                        Create New User
                    </h4>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        Back to Users
                    </a>
                </div>

                <div class="card-body">
                    <form method="POST" action="{{ route('admin.users.store') }}">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone_number" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control @error('phone_number') is-invalid @enderror"
                                           id="phone_number" name="phone_number" value="{{ old('phone_number') }}"
                                           placeholder="+256700000000" required>
                                    @error('phone_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                           id="password" name="password" required>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" 
                                           id="password_confirmation" name="password_confirmation" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role_id" class="form-label">Primary Role <span class="text-danger">*</span></label>
                                    <select class="form-select @error('role_id') is-invalid @enderror" 
                                            id="role_id" name="role_id" required>
                                        <option value="">Select a role</option>
                                        @foreach($roles as $role)
                                            <option value="{{ $role->id }}" {{ old('role_id') == $role->id ? 'selected' : '' }}>
                                                {{ $role->display_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('role_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_type" class="form-label">User Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('user_type') is-invalid @enderror" 
                                            id="user_type" name="user_type" required>
                                        <option value="">Select user type</option>
                                        <option value="admin" {{ old('user_type') == 'admin' ? 'selected' : '' }}>Admin</option>
                                        <option value="manager" {{ old('user_type') == 'manager' ? 'selected' : '' }}>Manager</option>
                                        <option value="viewer" {{ old('user_type') == 'viewer' ? 'selected' : '' }}>Viewer</option>
                                        <option value="agent" {{ old('user_type') == 'agent' ? 'selected' : '' }}>Agent</option>
                                    </select>
                                    @error('user_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" 
                                               name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active User
                                        </label>
                                    </div>
                                    <small class="text-muted">Inactive users cannot log in to the system</small>
                                </div>
                            </div>
                        </div>

                        <!-- Role Permissions Preview -->
                        <div class="mb-4">
                            <h6>Role Permissions Preview</h6>
                            <div id="permissions-preview" class="border rounded p-3 bg-light">
                                <p class="text-muted mb-0">Select a role to see its permissions</p>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-1"></i>
                                Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role_id');
    const permissionsPreview = document.getElementById('permissions-preview');
    
    const rolePermissions = {
        @foreach($roles as $role)
        '{{ $role->id }}': {
            name: '{{ $role->display_name }}',
            description: '{{ $role->description }}',
            permissions: @json($role->permissions ?? [])
        },
        @endforeach
    };
    
    roleSelect.addEventListener('change', function() {
        const selectedRoleId = this.value;
        
        if (selectedRoleId && rolePermissions[selectedRoleId]) {
            const role = rolePermissions[selectedRoleId];
            let html = `
                <div class="mb-2">
                    <strong>${role.name}</strong>
                    <p class="text-muted small mb-2">${role.description}</p>
                </div>
                <div class="row">
            `;
            
            if (role.permissions.length > 0) {
                role.permissions.forEach(permission => {
                    html += `
                        <div class="col-md-4 mb-1">
                            <span class="badge bg-primary">${permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                        </div>
                    `;
                });
            } else {
                html += '<div class="col-12"><span class="text-muted">No permissions assigned</span></div>';
            }
            
            html += '</div>';
            permissionsPreview.innerHTML = html;
        } else {
            permissionsPreview.innerHTML = '<p class="text-muted mb-0">Select a role to see its permissions</p>';
        }
    });
});
</script>
@endsection
