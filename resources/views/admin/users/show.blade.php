@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-person-circle text-primary me-2"></i>
                        User Details: {{ $user->name }}
                    </h4>
                    <div class="d-flex gap-2">
                        @if(auth()->user()->hasPermission('edit_users'))
                        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
                            <i class="bi bi-pencil me-1"></i>
                            Edit User
                        </a>
                        @endif
                        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>
                            Back to Users
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- User Information -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-person-fill text-info me-2"></i>
                                        User Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Name:</strong></div>
                                        <div class="col-sm-8">{{ $user->name }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Phone:</strong></div>
                                        <div class="col-sm-8">{{ $user->phone_number }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>User Type:</strong></div>
                                        <div class="col-sm-8">
                                            <span class="badge bg-secondary">{{ ucfirst($user->user_type) }}</span>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Status:</strong></div>
                                        <div class="col-sm-8">
                                            @if($user->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-danger">Inactive</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Created:</strong></div>
                                        <div class="col-sm-8">{{ $user->created_at->format('M d, Y H:i') }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Last Login:</strong></div>
                                        <div class="col-sm-8">
                                            @if($user->last_login_at)
                                                {{ $user->last_login_at->format('M d, Y H:i') }}
                                            @else
                                                <span class="text-muted">Never</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Role and Permissions -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-shield-check text-warning me-2"></i>
                                        Role & Permissions
                                    </h5>
                                </div>
                                <div class="card-body">
                                    @if($user->role)
                                        <div class="mb-3">
                                            <h6>Primary Role</h6>
                                            <span class="badge bg-info fs-6">{{ $user->role->display_name }}</span>
                                            <p class="text-muted small mt-2">{{ $user->role->description }}</p>
                                        </div>

                                        <div class="mb-3">
                                            <h6>Permissions</h6>
                                            @if($user->role->permissions && count($user->role->permissions) > 0)
                                                <div class="row">
                                                    @foreach($user->role->permissions as $permission)
                                                        <div class="col-12 mb-2">
                                                            <span class="badge bg-primary">
                                                                <i class="bi bi-check-circle me-1"></i>
                                                                {{ ucwords(str_replace('_', ' ', $permission)) }}
                                                            </span>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @else
                                                <p class="text-muted">No permissions assigned to this role</p>
                                            @endif
                                        </div>
                                    @else
                                        <div class="alert alert-warning">
                                            <i class="bi bi-exclamation-triangle me-2"></i>
                                            No role assigned to this user
                                        </div>
                                    @endif

                                    @if($user->roles->count() > 1)
                                        <div class="mb-3">
                                            <h6>Additional Roles</h6>
                                            @foreach($user->roles as $role)
                                                @if($role->id !== $user->role_id)
                                                    <span class="badge bg-secondary me-1">{{ $role->display_name }}</span>
                                                @endif
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    @if(auth()->user()->hasPermission('edit_users') && $user->id !== auth()->id())
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-gear text-secondary me-2"></i>
                                        Quick Actions
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex gap-2 flex-wrap">
                                        <form method="POST" action="{{ route('admin.users.toggle-status', $user) }}" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="btn btn-outline-{{ $user->is_active ? 'warning' : 'success' }}" 
                                                    onclick="return confirm('Are you sure you want to {{ $user->is_active ? 'deactivate' : 'activate' }} this user?')">
                                                <i class="bi bi-{{ $user->is_active ? 'pause' : 'play' }} me-1"></i>
                                                {{ $user->is_active ? 'Deactivate' : 'Activate' }} User
                                            </button>
                                        </form>

                                        @if(auth()->user()->hasPermission('delete_users'))
                                        <form method="POST" action="{{ route('admin.users.destroy', $user) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" 
                                                    onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                                <i class="bi bi-trash me-1"></i>
                                                Delete User
                                            </button>
                                        </form>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
